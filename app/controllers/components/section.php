<?php

class SectionComponent extends CakeObject {

  var $sectionSettings = array(
    'holidays' => array(
      'controller'   => 'holiday_types',
      'model'        => 'HolidayType',
      'associations' => array('Accommodation', 'Activities', 'Itineraries', 'Testimonials'),
    ),
    'destinations' => array(
      'controller'   => 'destinations',
      'model'        => 'Destination',
      'associations' => array('Accommodation', 'Activities', 'Images', 'Itineraries', 'Testimonials'),
    ),
    'landing_pages' => array(
      'controller'   => 'landing_pages',
      'model'        => 'LandingPage',
      'associations' => array('Accommodation', 'Activities', 'Images', 'Itineraries'),
    )
  );

  function startup(&$controller) {

    if (Configure::read('Runtime.site_or_admin') == 'admin') {
      return;
    }

    if (empty($controller->params['section'])) {
      return;
    }

    $section = $controller->params['section'];

    if (!isset($this->sectionSettings[$section])) {
      return;
    }

    $sectionSettings = $this->sectionSettings[$section];

    $sectionModel = $sectionSettings['model'];

    $sectionController = $sectionSettings['controller'];

    $sectionSlugParam = Inflector::underscore($sectionModel) . '_slug';

    $sectionSlug = $sectionData = $sectionId = null;

    if (isset($controller->params[$sectionSlugParam])) {
      $sectionSlug = $controller->params[$sectionSlugParam];
    } elseif ($section == 'destinations') {
      $sectionSlug = 'north_america';
    }

    $SectionModel = ClassRegistry::init($sectionSettings['model']);
    $SectionModel = new $sectionSettings['model'];

    if ($sectionSlug) {

     $sectionData = $this->_getSectionData($controller, $SectionModel, $sectionSlug);

      if (empty($sectionData)) {
        $this->cakeError('error404');
      }

      if (!empty($sectionData[$SectionModel->name]['youtube_playlist_id'])) {
          $sectionData['YoutubeVideo'] = $this->_getYoutubeVideos($controller, $sectionData[$SectionModel->name]['youtube_playlist_id']);
      }

      $sectionId = $sectionData[$sectionSettings['model']][$SectionModel->primaryKey];

      $controller->sectionId = $sectionId;
      $controller->sectionModel = $sectionModel;
      $controller->sectionData = $sectionData;

      if (!empty($sectionData['Testimonial'])) {
        $testimonialExtract = array();
        foreach ($sectionData['Testimonial'] as $key => $value) {
          if ($value['published']) {
            $controller->set('testimonialExtract', $value);
            break;
          }
        }
      }

      $controller->_setMeta($sectionData[$sectionSettings['model']]);

      if ($section == 'destinations') {
        if (($controller->name == 'Itineraries' && $controller->action == 'view') ||
            ($controller->name == 'Accommodations' && $controller->action == 'view')) {
          // Map Data is fetched from the Itinerary
        } else {
          $mapData = $this->_getMapData($controller, $SectionModel, $sectionId);
          $controller->set(compact('mapData'));
        }
      }
    }

    $navigation = $this->_getNavigation($controller, $SectionModel, $sectionId);

    $breadcrumbs = $this->_pathToSelected($navigation, $section);

    if (!empty($sectionData['BannerImage']['id'])) {
      $heroBannerImage = $sectionData['BannerImage'];
    }

    if ($section == 'destinations') {
      $navigation[0]['url'] = '/destinations';
    }

    $controller->selectedPrimaryNav = $section;

    $controller->set(compact('section', 'sectionController', 'sectionModel', 'sectionSlugParam', 'sectionSlug', 'sectionData', 'navigation', 'heroBannerImage', 'breadcrumbs'));
  }

  /**
   * Return and cache section data
   */
  protected function _getSectionData($controller, $model, $slug) {
    $find = function() use ($model, $slug) {
      return $model->getBySlug($slug);
    };

    return $controller->cacher(implode('_', array(
      strtolower($model->alias), $slug, 'section'
    )), $find);
  }

  /**
   * Return and cache section navigation
   */
  protected function _getNavigation($controller, $model, $id) {
    $getNavigation = function() use ($model, $id) {
      return $model->getNavigation($id);
    };

    return $controller->cacher(implode('_', array(
      strtolower($model->alias), $id, 'navigation'
    )), $getNavigation);
  }

  /**
   * Return and cache section map data
   */
  protected function _getMapData($controller, $model, $id) {
    $getMapData = function() use ($model, $id) {
      return $model->getMapData($id);
    };

    return $controller->cacher(implode('_', array(
      strtolower($model->alias), $id, 'map_data'
    )), $getMapData);
  }

  /**
   * Return and cache section map data
   */
  protected function _getYoutubeVideos($controller, $id) {
    $getVideos = function() use ($id) {
        App::import('Model', 'Gdata.YoutubeVideo');
        $youtubeVideo = new YoutubeVideo();
        return $youtubeVideo->find('all', array(
            'conditions' => array('playlist_id' => $id),
            'limit' => 12,
        ));
    };

    return $controller->cacher(implode('_', array(
      'youtube_playlist', $id
    )), $getVideos);
  }

  /**
   * Returns flat array nav tree of current record
   *
   * @return void
   * <AUTHOR>
  protected function _pathToSelected($nav, $section = '', $selected = array())
  {
    if (!is_array($nav)) {
      return $nav;
    }

    foreach ($nav as $v) {
      if (!empty($v['children'])) {
        $count = count($selected);

        $selected = $this->_pathToSelected($v['children'], $section, $selected);

        if (count($selected) > $count) {
          array_unshift($selected, array(
            'url'  => $this->_url($v['url'], $section),
            'text' => $v['text'],
          ));
        }
      }

      if (isset($v['selected']) && $v['selected'] === true) {
        $selected[] = array(
          'url'  => $this->_url($v['url'], $section),
          'text' => $v['text']
        );
      }
    }

    return $selected;
  }

  protected function _url($url = array(), $section)
  {
    if (!is_array($url)) {
      return $url;
    }

    $url['section'] = $section;

    return Router::url($url);
  }

  protected function _bannerTitle($breadcrumbs, $section)
  {
    $count = count($breadcrumbs);

    if ($count < 2) {
      return false;
    }

    if ($count <= 3 && $section === 'destinations') {
      return false;
    }

    return $breadcrumbs[$count - 2]['text'];
  }
}

?>
