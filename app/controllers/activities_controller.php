<?php

class ActivitiesController extends AppController {

  var $name = 'Activities';
  var $components = array('Section');

  function index() {

    $this->paginate['Activity'] = array(
      'limit' => 36,
      'recursive' => 0,
      'order' => 'name',
    );

    if ($this->params['section']) {

      $sectionModel = $this->Section->sectionSettings[$this->params['section']]['model'];

      $sectionForeignKey = Inflector::underscore($sectionModel).'_id';

      $joinTable = $this->{$this->modelClass}->hasAndBelongsToMany[$sectionModel]['joinTable'];

      $with = Inflector::classify($joinTable);

      $this->Activity->bindModel(array(
        'hasOne' => array(
          $with
        ),
      ), false);

      $this->paginate['Activity']['order'] = $with.'.order';

      $this->paginate['Activity']['conditions'] = array(
        $with.'.'.$sectionForeignKey => $this->sectionId
      );
    }

    $activities = $this->paginate('Activity');

    $this->_canonicalUrlForPaginated();

    $this->set(compact('activities'));

    $this->_setMeta('What to see and do');

  }

  function view() {

    $activity = $this->_findBySlug($this->params['activity_slug']);

    if (empty($activity)) {
      $this->cakeError('error404');
    }

    $this->_canonicalUrl(array(
      'activity_slug' => $this->params['activity_slug'],
      'section'       => 'destinations'
    ));

    $this->set(compact('activity'));

    $this->_setMeta($activity['Activity']);
  }

  /**
   * Return the itinerary from the url slug
   *
   * @return array
   **/
  protected function _findBySlug($slug) {

    $findBySlug = function() use ($slug) {

      return $this->Activity->getBySlug($slug);

    };

    return $this->cacher(implode('_', array(
      'activity', $slug
    )), $findBySlug);
  }


}
