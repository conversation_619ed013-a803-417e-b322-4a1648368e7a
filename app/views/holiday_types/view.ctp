<?php
$activitiesContent = false;
$accommodationsContent = false;
$itinerariesContent = false;
$youtubeVideosContent = false;

if (!empty($sectionData['Accommodation'])) {
    $accommodationsContent = $this->element('content_blocks--accommodation', array(
        'accommodations' => $sectionData['Accommodation'],
    ));
}

if (!empty($sectionData['Activity'])) {
    $activitiesContent = $this->element('content_blocks--activities', array(
        'activities' => $sectionData['Activity'],
    ));
}

if (!empty($sectionData['Itinerary'])) {
    $itinerariesContent = $this->element('content_blocks--itineraries', array(
        'itineraries' => $sectionData['Itinerary'],
    ));
}

if (!empty($sectionData['YoutubeVideo'])) {
    $youtubeVideosContent = $this->element('youtube_videos', array(
        'youtubeVideos' => $sectionData['YoutubeVideo']
    ));
}

echo $this->element('section_header', array(
    'accommodationsContent' => $accommodationsContent,
    'activitiesContent' => $activitiesContent,
    'imagesContent' => false,
    'itinerariesContent' => $itinerariesContent,
    'youtubeVideosContent' => $youtubeVideosContent,
));

if (empty($hideRightSidebar) || $hideRightSidebar !== true) {
    $theImage = !empty($holidayType['Image'][0]) ? $holidayType['Image'][0] : $holidayType['MainImage'];
    $sideBarContent = $this->element('right_sidebar', array(
        'modifier'        => 'destination',
        'sectionTitle'    => $holidayType['HolidayType']['name'],
        'theImage'        => $theImage,
        'hasTestimonials' => !empty($holidayType['Testimonial']),
        'hasImages'       => $theImage,
    ));
}

$sectionContent = $this->element('content_blocks', array(
  'contentBlocks' => $holidayType['ContentBlock']
));

echo $this->element('modules/section_content', array(
    'accommodationsContent' => $accommodationsContent,
    'activitiesContent' => $activitiesContent,
    'imagesContent' => false,
    'itinerariesContent' => $itinerariesContent,
    'sectionContent' => $sectionContent,
    'sideBarContent' => $sideBarContent,
    'youtubeVideosContent' => $youtubeVideosContent,
));
?>

<?php echo $this->element('section_footer') ?>

<?php echo $this->element('google_tracking'); ?>
