<?php $controlsCount = isset($controlsCount) ? $controlsCount : 3; ?>

<div class="image-viewer <?php if (isset($modifier)) { echo 'image-viewer--' . $modifier; } ?>">
  <?php
    echo $image->image($theImages[0], array(
      'divClass' => 'image-viewer__main js-image-viewer__main',
      'version'  => isset($mainVersion) ? $mainVersion : 'crop456x413'
    ));
  ?>

  <?php if (count($theImages) > 1): ?>
    <div class="image-viewer__wrapper">
      <div class="image-viewer__wrapper__images">
        <ul>
          <?php
            foreach ($theImages as $i) {
              echo $image->image($i, array(
                'wrapper'    => 'li',
                'divClass'   => 'image-viewer__wrapper__images__image',
                'noLightbox' => true,
                'version'    => 'crop130x118'
              ));
            }
          ?>
        </ul>
      </div>

      <?php if (count($theImages) >= $controlsCount): ?>
        <button class="image-viewer__nav js-image-viewer__nav image-viewer__nav--left js-image-viewer__nav--left">&lt;</button>
        <button class="image-viewer__nav js-image-viewer__nav image-viewer__nav--right">&gt;</button>

        <?php
        if (!empty($initViewer)) {
            $javascript->codeBlock("App.carousel.initialise($$('.image-viewer__wrapper__images').first());", array('inline' => false));
        }
        ?>
      <?php endif ?>
    </div>
  <?php endif ?>
</div>
