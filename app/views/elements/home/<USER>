<?php
$linkArray = array(
    'controller' => 'holiday_types',
    'action' => 'view',
    'section' => 'holidays'
);
?>
<div class="home-column home-column--ideas-and-inspiration">
  <h2>Ideas <br>And Inspiration?</h2>

  <div class="home-column__image-wrapper">
    <?php
      if ($image->hasImage($holidays[0]['Image'])) {
        $ideasImg = $image->image($holidays[0]['Image'], array(
            'version' => array(
                array(
                    'version' => 'crop250x140',
                    'respond' => '250w'
                ),
                array(
                    'version' => 'crop500x240',
                    'respond' => '500w'
                )
            ),
            'hideSizes' => true,
            'noLightbox' => true
        ));

        echo $html->link($ideasImg, array_merge(
            $linkArray,
            array(
                'holiday_type_slug' => $holidays[0]['HolidayType']['slug'],
            )
        ), array('escape' => false));
      }
    ?>
  </div>

  <p>
    Want some inspiration?<br>
    Want to try something new?
  </p>

  <ul>
    <?php foreach ($holidays as $a): ?>
      <li>
        <?php
        echo $html->link($a['HolidayType']['name'], array_merge(
            $linkArray,
            array(
                'holiday_type_slug' => $a['HolidayType']['slug'],
            )
        ));
        ?>
      </li>
    <?php endforeach ?>
  </ul>

  <p class="home-column__more-link">
    <?php echo $html->link('Choose your <b>holiday type</b>', array('controller' => 'holiday_types', 'action' => 'index', 'section' => 'holidays'), array(), false, false); ?>
  </p>
</div>
