/* global Object, YT, Wistia */

var App = window.App||{};

App.Video = (function () {
  'use strict';

  var Video = function ($el, options) {

    this.$ = $el;

    this.options = Object.extend(Video.defaults, options);

    this.init();

    return this.$;

  };

  Video.prototype.init = function() {

    if (this.options.type === 'youtube') {

      this.initYouTube();

    }

  };

  Video.prototype.initYouTube = function() {

    new YT.Player(this.$.identify(), {
      width: this.$.getLayout().get('width'),
      height: parseInt(this.$.getLayout().get('width') * 3/4, 10),
      videoId: this.$.readAttribute('data-video'),
      playerVars: { rel: 0 },
    });

  };

  Video.defaults = {
    type: 'youtube'
  };

  return Video;

}());
