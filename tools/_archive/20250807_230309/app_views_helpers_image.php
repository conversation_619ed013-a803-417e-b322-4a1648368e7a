<?php

class ImageHelper extends Helper
{

    public $path = array('uploads');

    public $helpers = array('Html');

    public function hasImage($image)
    {
        return isset($image['id']) ? 'has-image' : 'no-image';
    }

    public function image($image, $options = null)
    {
        $sizes = array();
        $srcSet = '';

        if (isset($image['Image'])) {
            $image = $image['Image'];
        }

        if (is_string($image)) {
            return $this->Html->image($image, $options);
        }

        if (!isset($image['id'])) {
            return;
        }

        $version = 'crop150x150';

        if (isset($options['version'])) {
            if (is_array($options['version'])) {
                $srcSet = $this->toSrcset($options['version'], $image);
                $version = $options['version'][0]['version'];
            } else {
                $version = $options['version'];
            }
        }

        $divClass = isset($options['divClass']) ? $options['divClass'] : '';

        $wrapper = isset($options['wrapper']) ? $options['wrapper'] : 'div';

        if (!isset($options['hideSizes']) || $options['hideSizes'] != true) {
            $sizes = $this->imageSizeFromVersion($version);
        }

        $out = $this->Html->image(
            RESOURCES_HOSTNAME . '/img/' . $this->filename($image, $version),
            array_merge(
                array('alt' => $image['alt']),
                $sizes,
                array('srcset' => $srcSet)
            )
        );

        if (isset($options['noLightbox'])) {
            return '<' . $wrapper . ' class="'.$divClass.'">'.$out.'</' . $wrapper . '>';
        }

        $html_options = array('class' => 'lightbox', 'title' => $image['alt']);

        $admin = Configure::read('Routing.admin');

        if (isset($this->params[$admin])) {
            $html_options['title'] = 'Image ID ' . $image['id'] . ' - ' . $html_options['title'];
        }

        if (isset($options['rel'])) {
            $html_options['rel'] = $options['rel'];
        }

        $out = $this->Html->link($out, RESOURCES_HOSTNAME.'/img/'.$this->filename($image, 'fit588x588'), $html_options, null, false);

        if (isset($this->params[$admin])) {
            $out .= '<p>'.$image['alt'].'</p>';
        }

        $out = '<' . $wrapper . ' class="'.$divClass.'">'.$out.'</' . $wrapper . '>';

        return $out;
    }

    private function toSrcset($versions, $image)
    {
        $images = array();

        foreach ($versions as $v) {
            $images[] = RESOURCES_HOSTNAME.'/img/'. $this->filename($image, $v['version']) . ' ' . $v['respond'];
        }

        return implode(', ', $images);
    }

    public function filename($image, $version)
    {
        return implode('/', $this->path).'/'.$image['id'].'_'.$version.'.'.$image['extension'];
    }

    public function video($id, $options = array())
    {
        if (empty($id)) {
            return;
        }

        $type = $this->videoType($id);

        $class = isset($options['class']) ? $options['class'] : '';

        $class .= ' ' . $this->videoClass($type, $id);

        return '<div class="'.$class.'" data-video="' . $id . '" itemscope itemtype="http://schema.org/MediaObject"></div>';
    }

    private function videoClass($type, $id)
    {
        return ($type === 'wistia') ? 'video-embed wistia_embed wistia_async_' . $id . ' videoFoam=true autoPlay=false' : 'video-embed js-youtube-embed';
    }

    private function videoType($id)
    {
        return (strlen($id) === 10) ? 'wistia' : 'youtube';
    }

    private function imageSizeFromVersion($version)
    {
        $sizes = array();

        if (preg_match('/[A-Za-z]+(\d+)x(\d+)/', $version, $matches)) {
            if (isset($matches[1]) && $matches[1] !== '0') {
                $sizes['width'] = $matches[1];
            }

            if (isset($matches[2]) && $matches[2] !== '0') {
                $sizes['height'] = $matches[2];
            }
        }

        return $sizes;
    }

    /**
    * Attempts to retrieve data for the given $key from the cache.
    * On failure, calls the passed function and writes the result
    * to cache and then returns it
    */
    private function cacher($key, $func)
    {
        if (($data = Cache::read($key, 'data')) === false) {
            $data = $func();
            Cache::write($key, $data, 'data');
        }

        return $data;
    }
}
