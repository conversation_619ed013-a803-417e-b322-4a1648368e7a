<?php
  $testimonialParams = array();

  if (isset($sectionSlug)) {
    $testimonialParams[$sectionSlugParam] = $sectionSlug;
    $testimonialParams['section']         = $section;
  }
?>

<?php echo $this->element('section_header') ?>

<div class="content-blocks content-blocks--testimonials <?php if (isset($section)) { echo 'content-blocks--section'; } ?>">
  <?php foreach ($testimonials as $t): ?>
    <div class="content-block">
      <div class="content-block__text">
        <h2>
          <?php
            echo $html->link($t['Testimonial']['title'], array_merge(
              $testimonialParams,
              array(
                'action'            => 'view',
                'testimonial_slug'  => $t['Testimonial']['slug']
              )
            ));
          ?>
        </h2>

        <?php if (!empty($t['Image']['id'])): ?>
          <div class="content-block__image content-block__image--left">
            <?php
            echo $image->image($t['Image'], array(
                'version' => 'crop190x190',
                'hideSizes' => true
            )); ?>
          </div>
        <?php endif ?>

        <?php echo $app->echoIf($t['Testimonial']['summary']); ?>

        <p class="content-block__link">
          <?php
            echo $html->link('Read <b>' . $t['Testimonial']['title'] . '</b>', array_merge(
              $testimonialParams,
              array(
                'action'            => 'view',
                'testimonial_slug'  => $t['Testimonial']['slug']
              )
            ), array('escape' => false));
          ?>
        </p>
      </div>
    </div>
  <?php endforeach ?>

  <?php
    $this->passedArgs = array_merge($this->passedArgs, $testimonialParams);

    echo $this->element('site_pagination_links');
  ?>
</div>

<?php echo $this->element('section_footer') ?>
