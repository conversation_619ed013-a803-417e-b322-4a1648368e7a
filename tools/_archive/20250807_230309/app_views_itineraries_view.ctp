<?php
echo $this->element('section_header', array(
    'modifier'      => 'itinerary'
))
?>

<?php if (!empty($mapData)): ?>
    <?php echo $this->element('modules/image_and_map', array(
        'modifier' => 'itinerary',
        'mapData' => $mapData,
        'hideTheImage' => isset($hideTheImage) ? $hideTheImage : false,
        'showMap' => isset($showMap) ? $showMap : false
    )); ?>
<?php endif; ?>

<header class="sub-section-header">
    <h2><?php echo $itinerary['Itinerary']['name']; ?></h2>

    <p class="sub-section-header__price">
        From: <b>&pound;<?php echo $itinerary['Itinerary']['from_price']; ?></b>
    </p>
</header>

<?php
echo $this->element('content_blocks', array(
    'modifier'      => 'itinerary',
    'contentBlocks' => $itinerary['ContentBlock']
));

echo $this->element('right_sidebar', array(
    'modifier'     => 'destination',
    'sectionTitle' => $sectionData[$sectionModel]['name'],
    'theImage'     => $sectionData['MainImage']
));
?>

<?php echo $this->element('trip_cta'); ?>

<div class="content-blocks content-blocks--itinerary-day">

    <?php foreach ($itinerary['ItineraryDay'] as $i): ?>
        <div class="content-block js-itinerary-day">
            <div class="content-block__text js-itinerary-day-text">
                <h3>
                    <?php echo $i['day_number']; ?> : <?php echo $i['name']; ?>
                </h3>

                <?php if(!empty($i['miles'])): ?>
                    <p class="content-block__text__miles">
                        Distance: <?php echo $i['miles']; ?> miles
                    </p>
                <?php endif; ?>

                <div class="content-block__image content-block__image--left">
                    <?php
                    echo $image->image($i['Image'], array(
                        'version' => 'crop370x370',
                        'hideSizes' => true
                    )); ?>
                </div>

                <?php echo $i['detail']; ?>
            </div>
            <button class="content-block__more js-itinerary-day-btn" type="button" name="button">Read more...</button>
        </div>
    <?php endforeach ?>

    <p class="content-block__link content-block__link--back">
        <?php
        echo $html->link('BACK TO INDEX', array(
            $sectionSlugParam => $sectionSlug,
            'section'         => $section,
            'action'          => 'index'
        ));
        ?>
    </p>
</div>


<?php echo $this->element('trip_cta'); ?>

<?php
echo $this->element('section_footer', array(
    'hideRightSidebar' => true
))
?>

<?php if (!empty($itinerary['Itinerary']['google_tracking'])) : ?>
    <?php echo $itinerary['Itinerary']['google_tracking']; ?>
<?php endif; ?>
