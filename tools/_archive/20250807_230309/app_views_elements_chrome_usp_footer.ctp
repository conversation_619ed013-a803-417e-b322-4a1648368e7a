<div class="usp-footer">
    <h2><b>The</b> Bon Voyage Holiday Promise</h2>
    <div class="usp-footer-row">
        <div class="usp-col">
            <div class="usp-title"><img src="/img/site/icons/promise/service.svg" alt="Hand holding Love Heart">
                <h3>Service</h3>
            </div>
            <p>Our friendly team of experts are here to help before, during and after your holiday</p>
        </div>
        <div class="usp-col">
            <div class="usp-title"><img src="/img/site/icons/promise/security.svg" alt="Shield with Check Mark">
                <h3>Security</h3>
            </div>
            <p>Your money and holiday are safe - we&rsquo;re AT<PERSON> bonded and members of ABTA</p>
        </div>
        <div class="usp-col">
            <div class="usp-title"><img src="/img/site/icons/promise/experience.svg" alt="Passport and Flight Tickets">
                <h3>Experience</h3>
            </div>
            <p>US and Canada specialists since 1979 - over 1,000 Transatlantic trips of our own</p>
        </div>
        <div class="usp-col">
            <div class="usp-title"><img src="/img/site/icons/promise/price.svg" alt="Padlock with Dollar Sign">
                <h3>Price</h3>
            </div>
            <p>No surcharges guaranteed and a Flexible Payment Plan to spread the cost</p>
        </div>
        <div class="usp-col">
            <div class="usp-title"><img src="/img/site/icons/promise/affordability.svg" alt="Piggy Bank">
                <h3>Afford&shy;ability</h3>
            </div>
            <p>Unrivalled supplier relationships mean optimum value for your money</p>
        </div>
        <div class="usp-col">
            <div class="usp-title"><img src="/img/site/icons/promise/bv_app.svg" alt="Mobile Phone App">
                <h3>BV App</h3>
            </div>
            <p>Your Bon Voyage holiday in your pocket – tickets, updates, landmarks and more</p>
        </div>
    </div>
</div>

<style>
.usp-footer {
    text-align: center;
}

.usp-footer h2 {
    font-size: 25px;
    line-height: 28px;
    color:#6C4000;
    font-weight: 300;
    text-transform: uppercase;
}
.usp-footer-row {
    display: flex;
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap; /* Ensure wrapping for all screen sizes */
    gap: 25px;
    max-width:1280px;
    padding: 10px 18px 40px;
    margin: 0 auto;
}
.usp-col {
    text-align: left;
    flex: 0 1 calc(50% - 12.5px); /* Default for narrow screens (two columns) */
    max-width: calc(50% - 12.5px);
    box-sizing: border-box;
}
.usp-title {
    display: flex;
    align-items: center;
    gap: 10px;
    hyphens: auto;
}
.usp-title h3 {
    margin: 0;
    font-size: 20px;
    line-height: 24px;
    color: #6C4000;
    font-weight: 400;
}
.usp-title img {
    width: 60px;
    height: 60px;
}
.usp-col p {
    font-size: 14px;
    line-height: 21px;
    color: #555;
}

/* Small screens (up to 550px) - 2 columns, 3 rows */
@media screen and (max-width: 550px) {
    .usp-col {
        flex: 0 1 100%; /* Each item is 100% width to create a single column */
        max-width: 100%;
    }
    .usp-title {
        gap: 15px;
    }
    .usp-title img {
        width: 40px;
        height: 40px;
    }
    .usp-title h3 {
        font-size: 22px;
        line-height: 28px;
        font-weight: 300;
    }
    .usp-col p {
        font-size: 15px;
        line-height: 22px;
        margin: 8px 0 5px 0;
    }
}

/* Small screens (up to 767px) - 2 columns, 3 rows */
@media screen and (min-width: 551px) and (max-width: 767px) {
    .usp-col {
        flex: 0 1 calc(50% - 12.5px); /* Each item is 50% width to create 2 columns */
        max-width: calc(50% - 12.5px);
    }
    .usp-title {
        gap: 15px;
    }
    .usp-title img {
        width: 80px;
        height: 80px;
    }
    .usp-title h3 {
        font-size: 25px;
        line-height: 30px;
        font-weight: 300;
    }
    .usp-col p {
        font-size: 15px;
        line-height: 22px;
    }
}

/* 2 rows of 3 columns for screens between 768px and 1000px */
@media screen and (min-width: 768px) and (max-width: 1000px) {
    .usp-col {
        flex: 0 1 calc(33.33% - 16.67px); /* Each item is one-third width, minus half the gap */
        max-width: calc(33.33% - 16.67px);
    }
}

/* 6 columns for screens wider than 1000px */
@media (min-width: 1000px) {
    .usp-col {
        flex: 0 1 calc(16.66% - 20.83px); /* Each item is one-sixth, minus half the 25px gap */
        max-width: calc(16.66% - 20.83px);
    }
}
</style>
