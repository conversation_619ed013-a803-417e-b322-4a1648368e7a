#!/bin/bash

# Here's a dynamic Bash script that achieves your requirements. It supports --files and --mode options, dynamically handles files, and provides clear logging.

# Script: deploy_manager.sh

# Get the current working directory (where the command is run from)
WORKING_DIR="$(pwd)"

# Source files directory
FILES_DIR="$WORKING_DIR/files"

# Default docroot path
DEFAULT_DOCROOT="/var/app/current"

# Directory for backup files (in the working directory)
ARCHIVE_DIR="$WORKING_DIR/_archive"

# This will be set up after argument parsing

# Function to display usage
usage() {
    echo
    echo -e "\033[1mUsage:\033[0m $0 --files \"file1 file2 ...\" --mode deploy|rollback [options]"
    echo
    echo "  -f, --files        Space-separated list of file paths to process"
    echo "  -m, --mode         Mode to run: deploy or rollback"
    echo "  -d, --docroot      Target document root (default: /var/app/current)"
    echo "  -n, --dry-run      Show what would happen without making changes"
    echo "  --force            Continue even if ownership change fails"
    echo "  --no-cache-clear   Skip clearing the application cache after deployment"
    echo "  --nested           Use nested structure in 'files' dir (default: flat)"
    echo "  --backup-dir DIR   Use specific backup directory for rollback"
    echo ""
    echo -e "\033[1mExamples:\033[0m"
    echo
    echo "  # Deploy flat files from 'files' directory (production):"
    echo "  $0 --files \"default.ctp main.css\" --mode deploy --dry-run"
    echo ""
    echo "  # Deploy nested files using --nested option:"
    echo "  $0 --files \"views/layouts/default.ctp webroot/css/main.css\" --mode deploy --nested"
    echo ""
    echo "  # Deploy with custom docroot (for testing):"
    echo "  $0 --files \"config/database.php\" --mode deploy --docroot ./app --nested"
    echo ""
    echo "  # Rollback using specific backup directory:"
    echo "  $0 --files \"default.ctp main.css\" --mode rollback --backup-dir _archive/20240804_143022"
    echo ""
    echo "  # File structure examples:"
    echo "  # Production: files/config/database.php → /var/app/current/config/database.php"
    echo "  # Testing:    files/config/database.php → ./app/config/database.php (with --docroot ./app)"
    echo ""
    exit 1
}

# Parse arguments
FILES=()
MODE=""
DOCROOT="$DEFAULT_DOCROOT"
DRY_RUN=false
FORCE=false
CLEAR_CACHE=true
NESTED=false
CUSTOM_BACKUP_DIR=""
while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--files)
            IFS=' ' read -r -a FILES <<< "$2"
            shift 2
            ;;
        -m|--mode)
            MODE="$2"
            shift 2
            ;;
        -d|--docroot)
            DOCROOT="$2"
            shift 2
            ;;
        -n|--dry-run)
            DRY_RUN=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --no-cache-clear)
            CLEAR_CACHE=false
            shift
            ;;
        --nested)
            NESTED=true
            shift
            ;;
        --backup-dir)
            CUSTOM_BACKUP_DIR="$2"
            shift 2
            ;;
        *)
            usage
            ;;
    esac
done

# Validate arguments
if [[ -z "$FILES" || -z "$MODE" ]]; then
    usage
fi

if [[ "$MODE" != "deploy" && "$MODE" != "rollback" ]]; then
    echo "Error: Invalid mode '$MODE'. Use 'deploy' or 'rollback'."
    exit 1
fi

# Expand docroot path (handle ~/ and other expansions)
DOCROOT=$(eval echo "$DOCROOT")

# Remove trailing slash from docroot to prevent double slashes
DOCROOT=${DOCROOT%/}

# Detect if we're in local testing mode (relative docroot path)
LOCAL_TESTING=false
if [[ "$DOCROOT" == ./* ]] || [[ "$DOCROOT" == ~/* ]] || [[ "$DOCROOT" != /* ]]; then
    LOCAL_TESTING=true
fi

# Set up backup directory based on mode and options
if [ "$MODE" = "rollback" ] && [ -n "$CUSTOM_BACKUP_DIR" ]; then
    # Use specified backup directory for rollback
    BACKUP_DIR="$WORKING_DIR/$CUSTOM_BACKUP_DIR"
    if [ ! -d "$BACKUP_DIR" ]; then
        echo "Error: Backup directory not found: $BACKUP_DIR"
        exit 1
    fi
else
    # Create timestamped backup directory for deployment
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    BACKUP_DIR="$ARCHIVE_DIR/$TIMESTAMP"

    # Ensure the archive and backup directories exist
    mkdir -p "$ARCHIVE_DIR"
    if [ "$MODE" = "deploy" ]; then
        mkdir -p "$BACKUP_DIR"
    fi
fi

# Check if files directory exists
if [ ! -d "$FILES_DIR" ]; then
    echo "Error: 'files' directory not found at $FILES_DIR"
    echo "Please create a 'files' directory containing your deployment files."
    if [ "$NESTED" = true ]; then
        echo "For nested structure: files/app/config/file.php"
    else
        echo "For flat structure: files/file.php"
    fi
    exit 1
fi

# Print operation header with reverse video
MODE_DISPLAY="$(tr '[:lower:]' '[:upper:]' <<< ${MODE:0:1})${MODE:1}"
STRUCTURE_TYPE="flat"
if [ "$NESTED" = true ]; then
    STRUCTURE_TYPE="nested"
fi

if [ "$DRY_RUN" = true ]; then
    printf "\nDRY RUN: %sing (%s structure)...\n\n" "$MODE_DISPLAY" "$STRUCTURE_TYPE"
else
    printf "\n%sing (%s structure)...\n\n" "$MODE_DISPLAY" "$STRUCTURE_TYPE"
fi

# Get total number of files
TOTAL_FILES=${#FILES[@]}
CURRENT_FILE=1

# Track deployment info for rollback command generation
DEPLOYED_FILES=()
BACKUP_CREATED=false

# Pre-deployment validation: Check if all target directories exist
echo -e "\033[0;34mValidating target directories...\033[0m"
MISSING_DIRS=()
for FILE in "${FILES[@]}"; do
    TARGET_DIR=$(dirname "$DOCROOT/$FILE")
    if [ ! -d "$TARGET_DIR" ]; then
        MISSING_DIRS+=("$TARGET_DIR")
    fi
done

# Remove duplicates from missing directories
if [ ${#MISSING_DIRS[@]} -gt 0 ]; then
    UNIQUE_MISSING_DIRS=($(printf "%s\n" "${MISSING_DIRS[@]}" | sort -u))

    echo -e "\033[0;31m❌ ERROR: Target directories do not exist!\033[0m"
    echo
    echo -e "\033[0;31mThe following directories are missing:\033[0m"
    for missing_dir in "${UNIQUE_MISSING_DIRS[@]}"; do
        echo -e "\033[0;31m  • $missing_dir\033[0m"
    done
    echo
    echo -e "\033[1;33mPlease create these directories first, or ensure your file paths are correct.\033[0m"
    echo -e "\033[1;33mExample: sudo mkdir -p \"$TARGET_DIR\"\033[0m"
    echo
    echo -e "\033[0;31mDeployment aborted to prevent errors.\033[0m"
    exit 1
fi

echo -e "\033[0;32m✅ All target directories exist\033[0m"

# Perform operations on each file
for FILE in "${FILES[@]}"; do
    BASENAME=$(basename "$FILE")

    # Determine source file path based on structure type
    if [ "$NESTED" = true ]; then
        SOURCE_FILE="$FILES_DIR/$FILE"
        BACKUP_NAME="${FILE//\//_}"  # Replace / with _ for backup filename
    else
        SOURCE_FILE="$FILES_DIR/$BASENAME"
        BACKUP_NAME="$BASENAME"
    fi

    # Check if source file exists
    if [ ! -f "$SOURCE_FILE" ]; then
        printf "[%d/%d] ❌ %s - Source file not found at %s\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE" "$SOURCE_FILE"
        ((CURRENT_FILE++))
        continue
    fi

    # Change ownership of the local file before any operations (skip in local testing)
    if [ "$DRY_RUN" = false ] && [ "$LOCAL_TESTING" = false ]; then
        if ! sudo chown webapp:webapp "$SOURCE_FILE" 2>/dev/null; then
            if [ "$FORCE" = true ]; then
                # Continue with warning when --force is used (original behavior)
                echo "Warning: Failed to change ownership, continuing anyway..."
            else
                printf "[%d/%d] ❌ %s - Failed to change ownership\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE"
                echo "Error: Failed to change ownership. Please check if group 'webapp' exists."
                echo "Use --force to continue anyway."
                exit 1
            fi
        fi
    elif [ "$LOCAL_TESTING" = true ]; then
        echo "  📝 Processing $FILE (local testing mode - skipping sudo)"
    fi

    case $MODE in
        deploy)
            # Check if destination file exists and handle accordingly
            if [ -f "$DOCROOT/$FILE" ]; then
                # Backup the existing file
                if [ "$DRY_RUN" = false ]; then
                    # Use sudo only in production mode
                    if [ "$LOCAL_TESTING" = true ]; then
                        BACKUP_CMD="cp"
                        DEPLOY_CMD="cp"
                    else
                        BACKUP_CMD="sudo cp"
                        DEPLOY_CMD="sudo cp"
                    fi

                    # Ensure backup directory exists
                    mkdir -p "$BACKUP_DIR" 2>/dev/null

                    if $BACKUP_CMD "$DOCROOT/$FILE" "$BACKUP_DIR/$BACKUP_NAME" 2>/dev/null; then
                        BACKUP_CREATED=true
                        # Deploy the new file
                        if $DEPLOY_CMD "$SOURCE_FILE" "$DOCROOT/$FILE" 2>/dev/null; then
                            printf "[%d/%d] ✅ %s\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE"
                            DEPLOYED_FILES+=("$FILE")
                        else
                            printf "[%d/%d] ❌ %s - Failed to deploy\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE"
                        fi
                    else
                        printf "[%d/%d] ❌ %s - Failed to create backup\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE"
                    fi
                else
                    printf "[%d/%d] ✅ %s (DRY RUN)\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE"
                    DEPLOYED_FILES+=("$FILE")
                fi
            else
                # No existing file - deploy new file
                if [ "$DRY_RUN" = false ]; then
                    # Ensure target directory exists
                    TARGET_DIR=$(dirname "$DOCROOT/$FILE")
                    if [ "$LOCAL_TESTING" = true ]; then
                        MKDIR_CMD="mkdir -p"
                        DEPLOY_CMD="cp"
                    else
                        MKDIR_CMD="sudo mkdir -p"
                        DEPLOY_CMD="sudo cp"
                    fi

                    if $MKDIR_CMD "$TARGET_DIR" 2>/dev/null && $DEPLOY_CMD "$SOURCE_FILE" "$DOCROOT/$FILE" 2>/dev/null; then
                        printf "[%d/%d] ✅ %s - No existing file\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE"
                        DEPLOYED_FILES+=("$FILE")
                    else
                        printf "[%d/%d] ❌ %s - Failed to deploy\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE"
                    fi
                else
                    printf "[%d/%d] ✅ %s - No existing file (DRY RUN)\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE"
                    DEPLOYED_FILES+=("$FILE")
                fi
            fi
            ;;
        rollback)
            # Rollback to the backup file
            if [[ -f "$BACKUP_DIR/$BACKUP_NAME" ]]; then
                if [ "$DRY_RUN" = false ]; then
                    # Use sudo only in production mode
                    if [ "$LOCAL_TESTING" = true ]; then
                        ROLLBACK_CMD="cp"
                    else
                        ROLLBACK_CMD="sudo cp"
                    fi

                    if $ROLLBACK_CMD "$BACKUP_DIR/$BACKUP_NAME" "$DOCROOT/$FILE" 2>/dev/null; then
                        printf "[%d/%d] ✅ %s\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE"
                    else
                        printf "[%d/%d] ❌ %s - Failed to rollback\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE"
                    fi
                else
                    printf "[%d/%d] ✅ %s (DRY RUN)\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE"
                fi
            else
                printf "[%d/%d] ❌ %s - No backup found (looking for %s)\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE" "$BACKUP_NAME"
            fi
            ;;
    esac

    ((CURRENT_FILE++))
done

# Clear cache after deployment if enabled
if { [ "$MODE" = "deploy" ] || [ "$MODE" = "rollback" ]; } && [ "$CLEAR_CACHE" = true ]; then
    if [ "$DRY_RUN" = false ]; then
        if [ "$LOCAL_TESTING" = true ]; then
            printf "\nSkipping cache clear (local testing mode)...\n"
            echo "✅ Cache clear skipped for local testing."
        else
            printf "\nClearing application cache...\n"
            if sudo find "$DOCROOT"/tmp/cache/ -type f -delete 2>/dev/null; then
                echo "✅ Cache cleared successfully."
            else
                echo "❌ Failed to clear cache."
            fi
        fi
    else
        printf "\nDRY RUN: Would clear application cache\n"
        if [ "$LOCAL_TESTING" = true ]; then
            echo "✅ Would run: find $DOCROOT"/tmp/cache/ -type f -delete
        else
            echo "✅ Would run: sudo find $DOCROOT"/tmp/cache/ -type f -delete
        fi
    fi
    echo
fi

# Generate deployment log and rollback command for successful deployments
if [ "$MODE" = "deploy" ] && [ ${#DEPLOYED_FILES[@]} -gt 0 ] && [ "$BACKUP_CREATED" = true ]; then
    if [ "$DRY_RUN" = false ]; then
        # Create deployment log
        LOG_FILE="$BACKUP_DIR/deployment.log"
        echo "Deployment Log - $TIMESTAMP" > "$LOG_FILE"
        echo "==============================" >> "$LOG_FILE"
        echo "Date: $(date)" >> "$LOG_FILE"
        echo "User: $(whoami)" >> "$LOG_FILE"
        echo "Structure: $STRUCTURE_TYPE" >> "$LOG_FILE"
        echo "Docroot: $DOCROOT" >> "$LOG_FILE"
        echo "" >> "$LOG_FILE"
        echo "Files deployed:" >> "$LOG_FILE"
        for deployed_file in "${DEPLOYED_FILES[@]}"; do
            echo "  - $deployed_file" >> "$LOG_FILE"
        done
        echo "" >> "$LOG_FILE"
        echo "Backup location: $BACKUP_DIR" >> "$LOG_FILE"

        # Generate rollback command
        FILES_LIST=$(printf '%s ' "${DEPLOYED_FILES[@]}")
        FILES_LIST=${FILES_LIST% }  # Remove trailing space
        NESTED_FLAG=""
        if [ "$NESTED" = true ]; then
            NESTED_FLAG=" --nested"
        fi

        DOCROOT_FLAG=""
        if [ "$LOCAL_TESTING" = true ]; then
            DOCROOT_FLAG=" --docroot $DOCROOT"
        fi

        ROLLBACK_CMD="$0 --files \"$FILES_LIST\" --mode rollback --backup-dir _archive/$TIMESTAMP$NESTED_FLAG$DOCROOT_FLAG"

        echo "" >> "$LOG_FILE"
        echo "Rollback command:" >> "$LOG_FILE"
        echo "$ROLLBACK_CMD" >> "$LOG_FILE"

        echo
        echo "📦 Backup created: $BACKUP_DIR"
        echo "📋 Deployment log: $LOG_FILE"
        echo
        echo "🔄 To rollback this deployment, run:"
        echo "$ROLLBACK_CMD"
        echo
    else
        echo
        echo "🧪 DRY RUN: Would create backup in _archive/$TIMESTAMP"
        echo "🧪 DRY RUN: Would generate rollback command for deployed files"
        echo
    fi
fi

echo "Operation '$MODE' complete"
echo

# How to Use:

# 	1.	Navigate to the folder containing the files to deploy
# 	2.	Run the script (from any location) with the required options
# 	•	For deployment:

# /path/to/deploy_manager.sh --files "app/views/layouts/prod/default.ctp app/webroot/js/tweaks.js" --mode deploy


# 	•	For rollback:

# /path/to/deploy_manager.sh --files "app/views/layouts/prod/default.ctp app/webroot/js/tweaks.js" --mode rollback


# 	•	To deploy without clearing cache:

# /path/to/deploy_manager.sh --files "app/views/layouts/prod/default.ctp" --mode deploy --no-cache-clear


# Key Features:

# 	1.	Location Independent: Script can be located anywhere, operates on files in current working directory
# 	2.	Dynamic File Handling: Matches filenames from the provided --files argument with files in working directory
# 	3.	Error Handling: Skips files not present in the working directory or missing backups
# 	4.	Mode Validation: Ensures valid modes (deploy or rollback) are used
# 	5.	Backup Management: Creates backups in ./_archive (in working directory) during deployment
# 	6.	Clear Logs: Prints actions for transparency with full paths
# 	7.	Cache Clearing: Automatically clears application cache after deployment (can be disabled)
#
# This script adapts to your specific requirements and ensures safe, repeatable file deployments and rollbacks.
