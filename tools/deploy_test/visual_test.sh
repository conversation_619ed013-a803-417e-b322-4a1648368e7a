#!/bin/bash

# Visual Deploy Test Script
# Tests deployment of template changes that are visible in the browser

echo "🎯 Visual Deploy Test for CakePHP Templates"
echo "============================================="
echo
echo "This test will deploy visual changes to the live CakePHP application"
echo "that you can see in your browser at bon-voyage.ddev.site"
echo
echo "📋 Files to be deployed:"
echo "  1. views/layouts/prod/default.ctp  - Adds YELLOW banner at top of all pages"
echo "  2. views/destinations/view.ctp     - Adds ORANGE banner on destination pages"
echo
echo "🌐 Where to see the changes:"
echo "  • Layout banner: ANY page on bon-voyage.ddev.site"
echo "  • Destination banner: Any destination page (e.g., /destinations/view/usa)"
echo
echo "⚠️  IMPORTANT: This will modify your live development site!"
echo "   Make sure you're ready to see visual changes on bon-voyage.ddev.site"
echo
echo "Press Enter to continue or Ctrl+C to cancel..."
read

echo
echo "🚀 Step 1: Deploying template changes..."
echo "========================================"
echo

# Deploy the template files
./deploy_manager.sh --files "views/layouts/prod/default.ctp views/destinations/view.ctp" --mode deploy --nested --docroot /Users/<USER>/Sites/bon-voyage/app

echo
echo "✅ Deployment complete!"
echo
echo "🌐 Now test in your browser:"
echo "=============================="
echo
echo "1. Open: http://bon-voyage.ddev.site"
echo "   → You should see a YELLOW banner at the top saying 'DEPLOY TEST ACTIVE'"
echo
echo "2. Navigate to any destination page, for example:"
echo "   → http://bon-voyage.ddev.site/destinations/view/usa"
echo "   → You should see BOTH banners:"
echo "     • YELLOW banner at top (from layout)"
echo "     • ORANGE banner below it (from destination view)"
echo
echo "📋 Rollback commands (save these!):"
echo "===================================="

# Get the latest backup directory
LATEST_BACKUP=$(ls -1t _archive/ | head -1)
if [ -n "$LATEST_BACKUP" ]; then
    echo
    echo "To rollback the layout file:"
    echo "./deploy_manager.sh --files \"views/layouts/prod/default.ctp\" --mode rollback --backup-dir _archive/$LATEST_BACKUP --nested --docroot /Users/<USER>/Sites/bon-voyage/app"
    echo
    echo "To rollback the destination view:"
    echo "./deploy_manager.sh --files \"views/destinations/view.ctp\" --mode rollback --backup-dir _archive/$LATEST_BACKUP --nested --docroot /Users/<USER>/Sites/bon-voyage/app"
    echo
    echo "To rollback both files:"
    echo "./deploy_manager.sh --files \"views/layouts/prod/default.ctp views/destinations/view.ctp\" --mode rollback --backup-dir _archive/$LATEST_BACKUP --nested --docroot /Users/<USER>/Sites/bon-voyage/app"
else
    echo "No backup directory found - check _archive/ folder for rollback commands"
fi

echo
echo "🎉 Visual test deployment complete!"
echo "Open your browser to see the changes!"
