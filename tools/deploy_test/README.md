# Deploy Manager Test Environment

This directory contains a complete test environment for the enhanced `deploy_manager.sh` script.

## 📁 Directory Structure

```
tools/deploy_test/
├── deploy_manager.sh          # Enhanced deploy script (local test version)
├── test_scenarios.sh          # Automated test script
├── README.md                  # This file
├── files/                     # Source files to deploy (NEW versions)
│   ├── app/
│   │   ├── config/
│   │   │   └── database.php   # NEW database config
│   │   └── views/
│   │       └── layouts/
│   │           └── default.ctp # NEW layout template
│   ├── webroot/
│   │   └── css/
│   │       └── main.css       # NEW CSS styles
│   └── plugins/
│       └── auth/
│           └── config.php     # NEW auth config
├── app/                       # Target deployment location (existing files)
│   ├── config/
│   │   └── database.php       # OLD database config (will be backed up)
│   ├── controllers/           # Empty directory
│   ├── views/
│   │   └── layouts/           # Empty directory (for new file test)
│   └── webroot/
│       └── css/
│           └── main.css       # OLD CSS styles (will be backed up)
└── _archive/                  # Backup directory (created during tests)
    └── [timestamp]/           # Timestamped backup folders
        ├── deployment.log     # Deployment details
        └── [backup files]     # Original files before deployment
```

## 🧪 Test Scenarios

### 1. **Missing Directory Error**
- **Test**: Deploy to `plugins/auth/config.php`
- **Expected**: Error because `app/plugins/` directory doesn't exist
- **Validates**: Pre-deployment directory validation

### 2. **New File Deployment**
- **Test**: Deploy `app/views/layouts/default.ctp`
- **Expected**: File deployed, no backup created (new file)
- **Validates**: New file deployment without backup

### 3. **Overwrite with Backup**
- **Test**: Deploy `app/config/database.php`
- **Expected**: Old file backed up, new file deployed
- **Validates**: Backup creation and file replacement

### 4. **Multiple File Deployment**
- **Test**: Deploy multiple files at once
- **Expected**: All files processed, backups created where needed
- **Validates**: Batch deployment with mixed scenarios

## 🚀 Running Tests

### Quick Test Run:
```bash
cd tools/deploy_test
./test_scenarios.sh
```

### Manual Testing:

#### Test Missing Directory (Should Error):
```bash
./deploy_manager.sh --files "plugins/auth/config.php" --mode deploy --nested --dry-run
```

#### Test New File Deployment:
```bash
./deploy_manager.sh --files "views/layouts/default.ctp" --mode deploy --nested --docroot ./app
```

#### Test Overwrite with Backup:
```bash
./deploy_manager.sh --files "config/database.php" --mode deploy --nested --docroot ./app
```

#### Test Rollback:
```bash
# Use the rollback command provided after deployment
./deploy_manager.sh --files "config/database.php" --mode rollback --backup-dir _archive/[timestamp] --nested --docroot ./app
```

## 🔍 Key Features Being Tested

### ✅ Pre-deployment Validation
- Checks if target directories exist before starting
- Prevents partial deployments due to missing directories
- Clear error messages with suggested fixes

### ✅ Timestamped Backups
- Each deployment gets its own backup folder
- Deployment logs with rollback commands
- No backup conflicts between deployments

### ✅ Nested Structure Support
- Files maintain their directory structure
- Backup names use underscore separation (e.g., `app_config_database.php`)
- Proper path handling for complex directory trees

### ✅ Local Testing Adaptations
- No `sudo` commands (works without admin privileges)
- Uses local `app/` directory as deployment target
- Skips ownership changes and cache clearing

## 📋 Expected Outputs

### Successful Deployment:
```
Validating target directories...
✅ All target directories exist

Deploying (nested structure)...

[1/1] ✅ app/config/database.php

📦 Backup created: /path/to/_archive/20240804_143022
📋 Deployment log: /path/to/_archive/20240804_143022/deployment.log

🔄 To rollback this deployment, run:
./deploy_manager.sh --files "app/config/database.php" --mode rollback --backup-dir _archive/20240804_143022 --nested
```

### Directory Validation Error:
```
Validating target directories...
❌ ERROR: Target directories do not exist!

The following directories are missing:
  • /path/to/app/plugins/auth

Please create these directories first, or ensure your file paths are correct.
Example: mkdir -p "/path/to/app/plugins/auth"

Deployment aborted to prevent errors.
```

## 🎯 This Test Environment Validates:

1. **Directory validation** prevents deployment errors
2. **Timestamped backups** provide version history
3. **Rollback commands** are automatically generated
4. **Nested structure** handling works correctly
5. **Mixed scenarios** (new files + overwrites) work together
6. **Local testing** works without production dependencies

Perfect for testing all deployment scenarios safely! 🛡️

## 🚀 Production vs Testing Usage

### **Production Deployment:**
```bash
# On production server (/ec2-user/deploy/)
# Files deployed to /var/app/current/ (default docroot)
./deploy_manager.sh --files "config/database.php views/layouts/default.ctp" --mode deploy --nested

# Rollback on production
./deploy_manager.sh --files "config/database.php" --mode rollback --backup-dir _archive/20240804_143022 --nested
```

### **Local Testing:**
```bash
# Override docroot for local testing
./deploy_manager.sh --files "config/database.php" --mode deploy --nested --docroot ./app

# Test rollback locally
./deploy_manager.sh --files "config/database.php" --mode rollback --backup-dir _archive/20240804_143022 --nested --docroot ./app
```

### **Key Differences:**

| Environment | Docroot | Files Location | Sudo Required |
|-------------|---------|----------------|---------------|
| **Production** | `/var/app/current` | `/ec2-user/deploy/files/` | Yes |
| **Testing** | `./app` (override) | `./files/` | No |

### **File Path Examples:**

#### Production:
- **Source**: `/ec2-user/deploy/files/config/database.php`
- **Target**: `/var/app/current/config/database.php`
- **Backup**: `/ec2-user/deploy/_archive/20240804_143022/config_database.php`

#### Testing:
- **Source**: `./files/config/database.php`
- **Target**: `./app/config/database.php`
- **Backup**: `./_archive/20240804_143022/config_database.php`
