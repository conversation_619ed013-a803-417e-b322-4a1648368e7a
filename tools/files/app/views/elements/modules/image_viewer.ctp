<?php $controlsCount = isset($controlsCount) ? $controlsCount : 3; ?>

<div class="image-viewer <?php if (isset($modifier)) { echo 'image-viewer--' . $modifier; } ?>">
  <?php
    // Main image should be lazy loaded if it's in a section (tabbed content)
    $mainImageOptions = array(
      'divClass' => 'image-viewer__main js-image-viewer__main',
      'version'  => isset($mainVersion) ? $mainVersion : 'crop456x413'
    );

    if (isset($modifier) && strpos($modifier, 'section') !== false) {
      $mainImageOptions['loading'] = 'lazy';
    }

    echo $image->image($theImages[0], $mainImageOptions);
  ?>

  <?php if (count($theImages) > 1): ?>
    <div class="image-viewer__wrapper">
      <div class="image-viewer__wrapper__images">
        <ul>
          <?php
            foreach ($theImages as $i) {
              echo $image->image($i, array(
                'wrapper'    => 'li',
                'divClass'   => 'image-viewer__wrapper__images__image',
                'noLightbox' => true,
                'version'    => 'crop130x118',
                'loading'    => 'lazy'
              ));
            }
          ?>
        </ul>
      </div>

      <?php if (count($theImages) >= $controlsCount): ?>
        <button class="image-viewer__nav js-image-viewer__nav image-viewer__nav--left js-image-viewer__nav--left">&lt;</button>
        <button class="image-viewer__nav js-image-viewer__nav image-viewer__nav--right">&gt;</button>

        <?php
        if (!empty($initViewer)) {
            $javascript->codeBlock("App.carousel.initialise($$('.image-viewer__wrapper__images').first());", array('inline' => false));
        }
        ?>
      <?php endif ?>
    </div>
  <?php endif ?>
</div>
