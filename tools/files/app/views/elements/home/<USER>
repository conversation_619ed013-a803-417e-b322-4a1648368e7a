<div class="home-column home-column--featured">
    <div class="home-column__image-wrapper">
        <?php echo $image->video($contentBlocks[0]['youtube_video_id'], array('loading' => 'lazy')); ?>
    </div>

    <?php echo $contentBlocks[0]['content'] ?>

    <p class="home-column__more-link">
        <?php echo $html->link('See more about <b>Bon Voyage</b>', array('controller' => 'pages', 'action' => 'view', 'page_slug' => 'about_bon_voyage', 'plugin' => null), array(), false, false); ?>
    </p>

    <?php
        // echo $this->element('modules/tile', array(
        //     'tileUrl' => $blogPost[0]['p']['guid'],
        //     'tileImage' => $blogPost[0][0]['full'],
        //     'tileTitle' => iconv('ISO-8859-1', 'UTF-8', $app->strtotitle($blogPost[0]['p']['post_title'])),
        //     'tileSummary' => iconv('ISO-8859-1', 'UTF-8', $text->truncate(strip_tags($blogPost[0]['p']['post_content']), 500, '...', false)),
        //
        // ));
    ?>
</div>
