/* global Object, YT, Wistia */

var App = window.App||{};

App.Video = (function () {
  'use strict';

  var Video = function ($el, options) {

    this.$ = $el;

    this.options = Object.extend(Video.defaults, options);

    this.init();

    return this.$;

  };

  Video.prototype.init = function() {

    if (this.options.type === 'youtube') {

      this.initYouTube();

    }

  };

  Video.prototype.initYouTube = function() {

    // Check if lazy loading is enabled
    var isLazy = this.$.readAttribute('data-loading') === 'lazy';

    if (isLazy) {
      // For lazy loading, create iframe manually with loading="lazy"
      var iframe = document.createElement('iframe');
      iframe.width = this.$.getLayout().get('width');
      iframe.height = parseInt(this.$.getLayout().get('width') * 3/4, 10);
      iframe.src = 'https://www.youtube.com/embed/' + this.$.readAttribute('data-video') + '?rel=0';
      iframe.frameBorder = '0';
      iframe.allowFullscreen = true;
      iframe.loading = 'lazy';

      // Replace the placeholder div with the iframe
      this.$.parentNode.replaceChild(iframe, this.$);
    } else {
      // Use the standard YouTube Player API
      new YT.Player(this.$.identify(), {
        width: this.$.getLayout().get('width'),
        height: parseInt(this.$.getLayout().get('width') * 3/4, 10),
        videoId: this.$.readAttribute('data-video'),
        playerVars: { rel: 0 },
      });
    }

  };

  Video.defaults = {
    type: 'youtube'
  };

  return Video;

}());
