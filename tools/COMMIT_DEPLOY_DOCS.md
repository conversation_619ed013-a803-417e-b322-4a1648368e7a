# Commit Deploy Tool Documentation

The `commit_deploy.sh` script extracts files changed between Git commits and copies them to a deployment directory, making it easy to deploy specific changes to production servers.

## 🎯 Purpose

- **Extract changed files** from Git commit ranges
- **Copy files** to a deployment directory (flat or nested structure)
- **Generate file lists** for deployment tools
- **Provide clipboard integration** for easy file management
- **Validate file integrity** with duplicate detection

## 📋 Syntax

```bash
./tools/commit_deploy.sh <start_commit> <end_commit> [options]
```

## 🔧 Arguments

### Required Arguments

| Argument | Description |
|----------|-------------|
| `start_commit` | Starting commit hash (inclusive) |
| `end_commit` | Ending commit hash (inclusive) - use `HEAD` for latest |

### Options

| Option | Description |
|--------|-------------|
| `-d, --deploy-dir DIR` | Target directory (default: `DEPLOY`) |
| `-s, --source-dir DIR` | Source directory/Git repository root (default: current dir) |
| `-n, --dry-run` | Preview changes without copying files |
| `-v, --verbose` | Show detailed output with file list |
| `-c, --no-clipboard` | Skip copying file list to clipboard |
| `-f, --force` | Skip duplicate filename check (dangerous) |
| `-p, --preserve-paths` | Maintain directory structure (default: flat) |
| `-h, --help` | Show help message |

## 📁 File Structure Options

### Flat Structure (Default)
All files copied to root of deploy directory:
```
DEPLOY/
├── database.php
├── main.css
├── config.php
└── index.php
```

### Nested Structure (--preserve-paths)
Maintains original directory hierarchy:
```
DEPLOY/
├── app/
│   └── config/
│       └── database.php
├── webroot/
│   └── css/
│       └── main.css
└── plugins/
    └── auth/
        └── config.php
```

## 🚀 Usage Examples

### Basic Usage
```bash
# Copy files changed between two commits (flat structure)
./tools/commit_deploy.sh abc123 def456

# Copy files from commit to HEAD
./tools/commit_deploy.sh abc123 HEAD

# Copy to custom directory
./tools/commit_deploy.sh abc123 def456 --deploy-dir STAGING

# Run from subdirectory (specify Git repository root)
./tools/commit_deploy.sh abc123 def456 --source-dir ../
```

### Advanced Usage
```bash
# Preview without copying
./tools/commit_deploy.sh abc123 def456 --dry-run --verbose

# Preserve directory structure
./tools/commit_deploy.sh abc123 def456 --preserve-paths

# Skip duplicate check (use with caution)
./tools/commit_deploy.sh abc123 def456 --force

# No clipboard integration
./tools/commit_deploy.sh abc123 def456 --no-clipboard

# Run from tools directory with parent as Git root
cd tools && ./commit_deploy.sh abc123 def456 --source-dir ../

# Complex example with all options
./tools/commit_deploy.sh abc123 HEAD --source-dir ../ --deploy-dir files --preserve-paths --dry-run --verbose
```

## 🛡️ Safety Features

### Duplicate Filename Detection
- **Automatically detects** files with same basename in different directories
- **Prevents overwrites** in flat structure mode
- **Shows conflict details** with full file paths
- **Suggests solutions** (use --preserve-paths or rename files)

### Example Duplicate Detection:
```bash
❌ ERROR: Duplicate filenames detected!

The following filenames appear multiple times:
  • config.php
    Found in:
      - app/config/config.php
      - plugins/auth/config.php

Options to resolve:
  • Use --preserve-paths to maintain directory structure
  • Rename conflicting files before deployment
  • Use --force to override this check (dangerous)
```

## 📋 Output Examples

### Successful Deployment
```bash
Getting changed files from abc123 to def456 (inclusive)...
Found 15 changed files

Checking for duplicate filenames (flat structure)...
✅ No duplicate filenames found

Copying files to DEPLOY (flat structure)...

  ✅ app/config/database.php → database.php
  ✅ webroot/css/main.css → main.css
  ✅ app/controllers/home_controller.php → home_controller.php

Summary:
  Successfully copied: 15 files
  File list copied to clipboard

Operation complete!
Files are ready in: DEPLOY
```

### Dry Run Output
```bash
Getting changed files from abc123 to def456 (inclusive)...
Found 8 changed files

Checking for duplicate filenames (flat structure)...
✅ No duplicate filenames found

Copying files to DEPLOY (flat structure)...

  📋 app/config/database.php → database.php (would copy)
  📋 webroot/css/main.css → main.css (would copy)

Summary:
  Would copy: 8 files
  File list copied to clipboard
```

## 🔄 Integration with Deploy Manager

The `commit_deploy.sh` tool works seamlessly with `deploy_manager.sh`:

### Workflow Example
```bash
# 1. Extract files from commits
./tools/commit_deploy.sh abc123 def456 --deploy-dir files

# 2. Deploy to production
./tools/deploy_manager.sh --files "database.php main.css config.php" --mode deploy

# 3. Rollback if needed (use command from deploy output)
./tools/deploy_manager.sh --files "database.php" --mode rollback --backup-dir _archive/20240807_143022
```

## 🖥️ Platform Support

### Clipboard Integration
- **macOS**: Uses `pbcopy`
- **Linux**: Uses `xclip` or `xsel`
- **Fallback**: Shows warning if clipboard tools unavailable

### Git Integration
- **Requires**: Git repository
- **Validates**: Commit hashes before processing
- **Supports**: Any Git commit reference (hash, branch, tag, HEAD)

## ⚠️ Important Notes

### Commit Range Behavior
- **Inclusive range**: Both start and end commits are included
- **File states**: Gets files as they exist at the end commit
- **Deleted files**: Skipped (only existing files are copied)

### File Handling
- **Preserves content**: Files copied as-is from working directory
- **No Git operations**: Doesn't modify repository state
- **Safe operation**: Only reads from Git, doesn't change history

### Error Handling
- **Missing files**: Skipped with warning
- **Invalid commits**: Script exits with error
- **Permission issues**: Reported with specific error messages

## 🔧 Troubleshooting

### Common Issues

**"Invalid commit hash"**
```bash
# Check commit exists
git log --oneline -10

# Use correct hash format
./tools/commit_deploy.sh a1b2c3d e4f5g6h
```

**"No files changed"**
```bash
# Verify commit range
git diff --name-only abc123..def456

# Check if commits are in correct order
git log --oneline abc123..def456
```

**"File not found"**
- File was deleted after the commit
- File exists in Git but not in working directory
- Check `git status` for uncommitted changes

## 📊 Performance

- **Fast operation**: Uses Git's efficient diff algorithms
- **Memory efficient**: Processes files one at a time
- **Scalable**: Handles large commit ranges and file counts
- **Progress feedback**: Shows real-time copy status

## 🔐 Security Considerations

- **Read-only Git operations**: Never modifies repository
- **File permissions**: Preserves original file permissions
- **Path validation**: Prevents directory traversal attacks
- **Safe defaults**: Flat structure prevents path conflicts

---

*This tool is part of the Bon Voyage deployment system and integrates with `deploy_manager.sh` for complete deployment workflows.*
